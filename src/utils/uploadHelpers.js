import { calculateMD5 } from './md5';

/**
 * Split a file into chunks for multipart upload
 * @param {File} file - The file to split
 * @param {number} chunkSize - Size of each chunk in bytes (default: 5MB)
 * @returns {Array<Blob>} Array of file chunks
 */
export const splitFileIntoChunks = (file, chunkSize = 5 * 1024 * 1024) => {
  const chunks = [];
  let start = 0;

  while (start < file.size) {
    const end = Math.min(start + chunkSize, file.size);
    const chunk = file.slice(start, end);
    chunks.push(chunk);
    start = end;
  }

  return chunks;
};

/**
 * Upload a single chunk to a pre-signed URL
 * @param {string} url - Pre-signed URL for upload
 * @param {Blob} chunk - File chunk to upload
 * @param {string} contentType - Content type of the file
 * @returns {Promise<Object>} Response with ETag
 */
export const uploadChunk = async (url, chunk, contentType) => {
  const response = await fetch(url, {
    method: 'PUT',
    body: chunk,
    headers: {
      'Content-Type': contentType,
    },
  });

  if (!response.ok) {
    throw new Error(`Failed to upload chunk: ${response.status} ${response.statusText}`);
  }

  // Extract ETag from response headers
  const etag = response.headers.get('ETag');
  return { etag: etag ? etag.replace(/"/g, '') : '' };
};

/**
 * Upload a file using multipart upload with progress tracking
 * @param {Object} options - Upload options
 * @param {File} options.file - File to upload
 * @param {Function} options.initiateUpload - Function to initiate multipart upload
 * @param {Function} options.getPartUrl - Function to get upload URL for a part
 * @param {Function} options.completeUpload - Function to complete multipart upload
 * @param {Function} options.onProgress - Progress callback function
 * @param {number} options.chunkSize - Size of each chunk in bytes (default: 5MB)
 * @returns {Promise<Object>} Upload result
 */
export const multipartUpload = async ({
  file,
  initiateUpload,
  getPartUrl,
  completeUpload,
  onProgress = () => { },
  chunkSize = 5 * 1024 * 1024,
  metadata = {},
}) => {
  try {
    // Step 1: Initiate multipart upload
    const initiateResponse = await initiateUpload({
      filename: file.name,
      contentType: file.type,
      size: file.size,
      ...metadata,
    });

    const { uploadId, resource } = initiateResponse.data;

    // Step 2: Split file into chunks
    const chunks = splitFileIntoChunks(file, chunkSize);
    const totalChunks = chunks.length;
    const parts = [];

    // Step 3: Upload each chunk
    for (let i = 0; i < totalChunks; i++) {
      const partNumber = i + 1;
      const chunk = chunks[i];

      // Get pre-signed URL for this part
      const urlResponse = await getPartUrl({
        uploadId,
        partNumber,
      });

      const uploadUrl = urlResponse.data.url;

      // Upload the chunk
      const { etag } = await uploadChunk(uploadUrl, chunk, file.type);

      parts.push({
        partNumber,
        etag,
      });

      // Report progress
      onProgress((i + 1) / totalChunks * 100);
    }

    // Step 4: Complete multipart upload
    await completeUpload({
      uploadId,
      parts,
    });

    return resource;
  } catch (error) {
    console.error('Multipart upload failed:', error);
    throw error;
  }
};

/**
 * Format file size in human-readable format
 * @param {number} bytes - File size in bytes
 * @returns {string} Formatted file size
 */
export const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

/**
 * Get file extension from filename
 * @param {string} filename - File name
 * @returns {string} File extension
 */
export const getFileExtension = (filename) => {
  return filename.split('.').pop().toLowerCase();
};

/**
 * Check if file is an image
 * @param {File} file - File to check
 * @returns {boolean} True if file is an image
 */
export const isImageFile = (file) => {
  const imageTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml'];
  return imageTypes.includes(file.type);
};

/**
 * Check if file is a video
 * @param {File} file - File to check
 * @returns {boolean} True if file is a video
 */
export const isVideoFile = (file) => {
  const videoTypes = [
    'video/mp4',
    'video/webm',
    'video/ogg',
    'video/quicktime', // .mov
    'video/x-msvideo', // .avi
    'video/x-matroska' // .mkv
  ];
  return videoTypes.includes(file.type);
};

/**
 * Detect file type category
 * @param {File} file - File to categorize
 * @returns {string} File category ('image', 'video', 'other')
 */
export const detectFileType = (file) => {
  if (isImageFile(file)) return 'image';
  if (isVideoFile(file)) return 'video';
  return 'other';
};

/**
 * Determine if file should use multipart upload based on size and type
 * @param {File} file - File to check
 * @param {number} threshold - Size threshold in bytes (default: 10MB)
 * @returns {boolean} True if should use multipart upload
 */
export const shouldUseMultipartUpload = (file, threshold = 10 * 1024 * 1024) => {
  // Always use multipart for videos regardless of size
  if (isVideoFile(file)) return true;

  // Use multipart for large images
  if (isImageFile(file) && file.size > threshold) return true;

  // Use multipart for other large files
  return file.size > threshold;
};

/**
 * Convert file to base64 string
 * @param {File} file - File to convert
 * @returns {Promise<string>} Base64 string representation of the file
 */
export const fileToBase64 = (file) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result);
    reader.onerror = error => reject(error);
  });
};



/**
 * Separate files by type for multiple image / single video handling
 * @param {Array<File>} files - Array of files to separate
 * @returns {Object} Object with images and videos arrays
 */
export const separateFilesByType = (files) => {
  const images = [];
  const videos = [];

  files.forEach(file => {
    if (isImageFile(file)) {
      images.push(file);
    } else if (isVideoFile(file)) {
      videos.push(file);
    }
  });

  return { images, videos };
};

/**
 * Upload a file using direct upload (for images)
 * @param {Object} options - Upload options
 * @param {File} options.file - File to upload
 * @param {Function} options.getUploadUrl - Function to get upload URL
 * @param {Function} options.onProgress - Progress callback function
 * @returns {Promise<Object>} Upload result with resource details
 */
export const directUpload = async ({
  file,
  getUploadUrl,
  onProgress = () => {},
}) => {
  try {
    // Step 1: Calculate MD5 hash
    onProgress(10); // 10% - calculating hash
    const contentMd5 = await calculateMD5(file);

    // Step 2: Get upload URL and resource details
    onProgress(20); // 20% - getting upload URL
    const uploadResponse = await getUploadUrl({
      filename: file.name,
      content_type: file.type,
      content_md5: contentMd5,
      content_size: file.size.toString(),
      resource_type: "Public",
    });

    const { url, method, resource } = uploadResponse.data;

    // Step 3: Upload file to AWS pre-signed URL using raw file data
    onProgress(30); // 30% - starting upload

    // Use fetch with progress simulation for better UX
    return new Promise((resolve, reject) => {
      // Simulate progress during upload
      const progressInterval = setInterval(() => {
        onProgress(Math.min(90, onProgress.currentProgress + 5));
      }, 200);

      fetch(url, {
        method: method || 'PUT',
        body: file,
        headers: {
          'Content-Type': file.type,
          'Content-MD5': contentMd5,
        },
      })
      .then(response => {
        clearInterval(progressInterval);

        if (!response.ok) {
          throw new Error(`Upload failed with status: ${response.status} - ${response.statusText}`);
        }

        onProgress(100); // 100% - upload complete
        resolve(resource);
      })
      .catch(error => {
        clearInterval(progressInterval);
        console.error('Fetch upload error:', error);
        reject(new Error(`Upload failed: ${error.message}`));
      });

      // Initialize progress tracking
      onProgress.currentProgress = 30;
    });

  } catch (error) {
    console.error('Direct upload failed:', error);
    throw error;
  }
};